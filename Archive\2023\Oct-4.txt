

================================
ACTIVATION
================================

MVT2915477
MVT2915507
MVT2921014
MVT2921149
MVT2922450
MVT2922695
MVT2922714
MVT2922721
MVT2923263
MVT2926800
MVT2927179
MVT2931986



1323508



End Of Day Orders
=================



MVT
===
1004819604 EPL



Disco
=====
1003473696
1004795768



BandWidth Upgrade
=================



Underlay
========




Service Accepting
=================



================================
             ORDERS
================================


Luis - activate
1456
1004615510


Jean - activate
4602
OSite_721978_1


Joe - activate
5338
1004689782


Rosendo - activate
1755
1004744780


Fred - activate
3595
130499020
enable Bundle-Ether1.3925
enable GigabitEthernet0/0/0/7.3925
accept


Anthony - activate
3322
OSite_721101_1
enable ae102.3959
accept






================================
SURVEYS - INFORMATION - NOT DONE
================================



















================================
	    UNDERLAY
================================

++++ TTU ++++

Customer:  Cunningham Restaurant Group - 1004460506 
Service: BVE
CPE: ************** 
UNI: port 1
VLAN: 3976


-created ip-int and deleted after pinging

Pinging ************* with 32 bytes of data:                                                                            Reply from *************: bytes=32 time=102ms TTL=52                                                                    Reply from *************: bytes=32 time=75ms TTL=52                                                                     Reply from *************: bytes=32 time=96ms TTL=52                                                                     Reply from *************: bytes=32 time=116ms TTL=52 

interface create ip-interface av-ref ip *************/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3976



-loaded config on port 1


set groups BVE-TEST-L3IRB interfaces irb unit 3993 family inet address *************/30
set groups BVE-TEST-L3IRB bridge-domains vlan3993 routing-interface irb.3993
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB







================================
	     OTHERS
================================

++++ TTU ++++

Customer: The Colony Hotel Palm Beach - **********
Service: EDI
CPE: ************** - PLBHFLIJ03W
UNI: Port 1
VLAN: 3808

Notes
=======

According to desing the CE should connect to sag12 on port GE-0/0/0/30
but there is no sfp slotted on port GE-0/0/0/30 - preconfig is loaded
order is rejected for no CE online and no SFP slotted at the HE




RP/0/RP0/CPU0:sag12.northpoint.fl.pompano#sh controll opt 0/0/0/30
Wed Sep 13 20:55:52.342 UTC

 Controller State: Down 

 Transport Admin State: In Service 

 Laser State: Off 

         Optics not present
         Optics Type: Unavailable
         DWDM Carrier Info: Unavailable, MSA ITU Channel= Unavailable, Frequency= Unavailable , Wavelength= Unavailable 
         TX Power = Unavailable 
         RX Power = Unavailable 




