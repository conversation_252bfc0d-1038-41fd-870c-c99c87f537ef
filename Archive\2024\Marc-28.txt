
cadapass auth -a netengmguzma002 -s XgBJZ14mw2IuCYBD6O6Z7KLED9LJks00https://ev01.b.comcast.net/menu.html

jump cada --password-only --username=mguzma002 --env=prod null
eval $(ssh-agent)
ssh-add


<PERSON> - 4109
<PERSON> - 2910
<PERSON> - 4067
<PERSON> - 3640
<PERSON> - 3751
Aaron - 5075
<PERSON> - 4733
Travis <PERSON> - 4051
Christ Chong - 2041
Sadoc - 2730
Alex - 1499
<PERSON> 5095
<PERSON> 2425
Mike <PERSON> 1459
<PERSON> 4138
<PERSON> 3104
Luis 2333 hialeah
<PERSON> 1132 
<PERSON> 5732
Andy  <PERSON> 3320
<PERSON> 3322
<PERSON><PERSON>a 2025
<PERSON> 2910
<PERSON> 2679

================================
             ORDERS
================================

Jaime - 1162 1004210092 customer accepting - complete order when task is ready
EOD 1004761030 waiting on PM old <PERSON> has not been removed - PM was waiting on activecore to be canceled
work day - side jobs gigs
Mike 2427 1004851794 - waiting on the end of the EPL to be installed
Mike - 256-656-8541 - 1005039956 B<PERSON> follow up
-

<PERSON> 2679 130308226

Julio 4361 38143416 BVE - waiting

Robert 1113 1004877592 EDI Y

Brent 1004846876 EPL Y

Brian 1005044704 












1/1/27:958
service 280









Brad - 8666248624 option 1 - 1004749226 - follow up with this order two way mac issue

================================
SURVEYS - INFORMATION - NOT DONE
================================





Service Accepting
=================
Randy 2397455459 - 1004822580



================================
OTHER ORDERS COMPLETED
================================





================================
	    UNDERLAY
================================

++++ TTU ++++

Customer: DREAM MARKET - OSite_680831_1 
Service: BVE
CPE: *************
UNI: port 2
VLAN: 3999

-created virtual interface and deleted after pinging
-p2p pinging on virtual interface
-enable GigabitEthernet0/0/0/5.3999

    

interface create ip-interface av-ref ip ***********/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3760



set groups BVE-TEST-L3IRB interfaces irb unit 3990 family inet address ************/30
set groups BVE-TEST-L3IRB bridge-domains vlan3990 routing-interface irb.3990
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB







================================
	     OTHERS
================================

++++ TTU ++++

Customer: The Colony Hotel Palm Beach - **********
Service: EDI
CPE: ************** - PLBHFLIJ03W
UNI: Port 1
VLAN: 3808

Notes
=======

According to desing the CE should connect to sag12 on port GE-0/0/0/30
but there is no sfp slotted on port GE-0/0/0/30 - preconfig is loaded
order is rejected for no CE online and no SFP slotted at the HE




RP/0/RP0/CPU0:sag12.northpoint.fl.pompano#sh controll opt 0/0/0/30
Wed Sep 13 20:55:52.342 UTC

 Controller State: Down 

 Transport Admin State: In Service 

 Laser State: Off 

         Optics not present
         Optics Type: Unavailable
         DWDM Carrier Info: Unavailable, MSA ITU Channel= Unavailable, Frequency= Unavailable , Wavelength= Unavailable 
         TX Power = Unavailable 
         RX Power = Unavailable 




