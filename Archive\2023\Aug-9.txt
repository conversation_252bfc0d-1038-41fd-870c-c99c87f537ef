

================================
ACTIVATION
================================
MVT2909706
MVT2909752
MVT2909885



Mathew - customer - offnet
4133879021
1004676720
accepting

Jason Helm- customer 
6164223358
1004685238
accepting

Lyndon - customer accepting
activatecore
1004589664

Don - 
5070
130397225


================================


Mat - activate - fix fqdn
3376
1004400454
enable GigabitEthernet0/0/0/13.3932
not accepting
fix radius


Justin - activate
3098
1004330420
accepting


Matt - activate
2078
130484290
enable GigabitEthernet0/0/0/21.3929
enable Bundle-Ether1.3929
accepting


thony - activate
1522
1004595158
enable Bundle-Ether1.3720
accepting


Dan - activate
3640
**********
accepting


Craig - actiate
2679
**********
accepting


Lorenzo - activate
2725
OSite_650700_1
not accepting


John - activate
5398
OSite_694266_1
not accepting















================================
SURVEYS - INFORMATION - NOT DONE
================================



Brent - 
2492185
ch48sh 


Deen - activate - not done
1656
BSNE-25367


Dan - no light at the HE
4067
OSite_602698_1



















===== TTU =====

Customer: First Century Bank - *********
Service: SIP
CPE: *************
UNI: ge-0/0/1
VLAN: 3891

-loaded config on port 2
-created ip-int and deleted after pinging

Reply from ***********: bytes=32 time=72ms TTL=50                                               
Reply from ***********: bytes=32 time=75ms TTL=50                                               
Reply from ***********: bytes=32 time=71ms TTL=50                                               
Reply from ***********: bytes=32 time=71ms TTL=50


interface create ip-interface av-ref ip ************/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3900


set groups BVE-TEST-L3IRB interfaces irb unit 3891 family inet address ***********/30
set groups BVE-TEST-L3IRB bridge-domains vlan3891 routing-interface irb.3891
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB



















===== TTU =====

Customer: Baptist Health South Florida  - **********
Service: EPL
CPE: **************
EVC: 38.VLXP.056507..CBCL..
MVT: MVT2904056

-removed cpe from monitoring service
 ce-wpbiflle00w.cpe.fl.pompano.comcast.net 	************** 	Decommission 	MVT2904056 	7/26/2023 11:27 PM

-multiple services on MIAMFLFR04W





-unable to remove from MVT - another active service on port 2



















