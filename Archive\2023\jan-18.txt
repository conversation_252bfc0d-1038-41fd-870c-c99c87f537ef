
modem: c0:94:35:e6:40:bd
nid-cpe-mmlkflmt00w.metroeohfc.fl.npls.comcast.net
ciena: d4:39:b8:08:c9:bf - d4:39:b8:08:c9:b4
ciena mngt: 73.139.13.122

+-------------- CHASSIS MAC PARTITIONS --------------+
| Partition        | Base MAC           | Block Size |
+------------------+--------------------+------------+
| Chassis          | d4:39:b8:08:c9:b0  | 16         |
+------------------+--------------------+------------+
| L3 Interface     | d4:39:b8:08:c9:b1  | 1          |
| Port Base        | d4:39:b8:08:c9:b2  | 3          |
| Benchmark        | d4:39:b8:08:c9:b5  | 1          |
| Auxiliary I/F    | d4:39:b8:08:c9:be  | 1          |
| Remote Mgmt      | d4:39:b8:08:c9:bf  | 1          |
+------------------+--------------------+------------+


Jose - customer IT - called wrong department
130455211
customer called in after activation - referred him to customer care. He wants to add a sfp in port 1 instead of using port 1 copper. verified he had the correct p2p ip block. Customer verified traffic on their laptop. I also saw traffic on port 1 as well.


Safe - customer IT - called wrong departmen
130363172


Justin
4216
130427035



john
1004349098
8774073224

greig
2679
130467256
enable int ge-0/0/18


carel - wrong distro
8262
2389068

Rey - turn port up
8028
2389312
tune to channel 55


James Karnel - customer
130495407


Morris
2418483


john
5066
2386585

Aren
130311685


jacob 
3320
2423114


luis - wrong distro
1456
1004287880


alen - loaded config on the ciena
2670
130442515


Andy - verify ports
3320
2408800


thony - port turnup
130486544
enable interfaces ge-0/2/2


joe - tech to customer was using the incorrect p2p ip block
5065
130485634


Eric - close
2342996


Richad - done -activate 
4466
2399777


Alex - close order
1499
1004242316

Dina Sandy
4043517654


Lorenzo
2725
130462276


Eugene activate -done
4602
2392006
enable Bundle-Ether1.3571


john - active done
3233
2408172
enable GigabitEthernet0/0/0/35.3762 - Bundle-Ether1.3762


phil
1844
130467770


























