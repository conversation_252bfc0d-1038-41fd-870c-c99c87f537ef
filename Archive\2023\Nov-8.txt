
cadapass auth -a netengmguzma002 -s XgBJZ14mw2IuCYBD6O6Z7KLED9LJks00
env $(ssh-agent)
ssh-add
jump cada --password-only --username=MGuzma002 --env=prod null

PzfQiW#+8i)5XQD@)YjBvfTDefFGu1Zn
53BS26I3
================================
ACTIVATION
================================

MVT2915477
MVT2915507
MVT2921014
MVT2921149
MVT2922450
MVT2922695
MVT2922714
MVT2922721
MVT2923263
MVT2926800
MVT2927179
MVT2931986
MVT2934080
MVT2937316
MVT2938968
MVT2938986



my ticket SR019361257


End Of Day Orders
=================



MVT
===
OSite_539528_1
1004848082 - ce-atlagabm08w.cpe.ga.atlanta.comcast.net - MVT2943930

soag16.area4.il.chicago.comcast.net
<EMAIL>


Disco
=====





BandWidth Upgrade
=================







Underlay
========
1004850490 - waiting
OSite_680831_1 - verify ip



Service Accepting
=================




Jira Tickets
=================
CT-20450



================================
             ORDERS
================================


James
5173
OSite_705289_1
n


Josh - bve
5076
36450493


justin
5036
1004611002
a


Thony
1522
1004559022
a


Jason
7736800029
OSite_730398_1






================================
SURVEYS - INFORMATION - NOT DONE
================================


Stuew -
6303572047
OSite_702718_1












================================
	    UNDERLAY
================================

++++ TTU ++++

Customer: DREAM MARKET - OSite_680831_1 
Service: BVE
CPE: *************
UNI: port 2
VLAN: 3999

-created virtual interface and deleted after pinging
-p2p pinging on virtual interface
-enable GigabitEthernet0/0/0/5.3999

    

interface create ip-interface av-ref ip ************/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3999



set groups BVE-TEST-L3IRB interfaces irb unit 3990 family inet address ************/30
set groups BVE-TEST-L3IRB bridge-domains vlan3990 routing-interface irb.3990
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB







================================
	     OTHERS
================================

++++ TTU ++++

Customer: The Colony Hotel Palm Beach - **********
Service: EDI
CPE: ************** - PLBHFLIJ03W
UNI: Port 1
VLAN: 3808

Notes
=======

According to desing the CE should connect to sag12 on port GE-0/0/0/30
but there is no sfp slotted on port GE-0/0/0/30 - preconfig is loaded
order is rejected for no CE online and no SFP slotted at the HE




RP/0/RP0/CPU0:sag12.northpoint.fl.pompano#sh controll opt 0/0/0/30
Wed Sep 13 20:55:52.342 UTC

 Controller State: Down 

 Transport Admin State: In Service 

 Laser State: Off 

         Optics not present
         Optics Type: Unavailable
         DWDM Carrier Info: Unavailable, MSA ITU Channel= Unavailable, Frequency= Unavailable , Wavelength= Unavailable 
         TX Power = Unavailable 
         RX Power = Unavailable 




