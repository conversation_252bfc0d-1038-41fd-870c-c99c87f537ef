

================================
ACTIVATION
================================



MVT2909885
MVT2910322
MVT2910342



================================

Austin - bve 
4175
35844359
183211


Matt - bve
5446
********
183730


Scott - bve
3465
********
183694


Omar - activate
1228
2445953
enable TenGigE0/5/0/1.3731
enable TenGigE0/5/0/13.3731
accepting


Ray - activate
3286
OSite_713189_1


Tremayne - activate
1819
**********
not accepting


Dan - activate
1415
**********
accepting











================================
SURVEYS - INFORMATION - NOT DONE
================================


Farid - survey
1032
2508396





















===== TTU =====

Customer: First Century Bank - *********
Service: SIP
CPE: *************
UNI: ge-0/0/1
VLAN: 3891

-loaded config on port 2
-created ip-int and deleted after pinging

Reply from ***********: bytes=32 time=72ms TTL=50                                               
Reply from ***********: bytes=32 time=75ms TTL=50                                               
Reply from ***********: bytes=32 time=71ms TTL=50                                               
Reply from ***********: bytes=32 time=71ms TTL=50


interface create ip-interface av-ref ip ************/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3900


set groups BVE-TEST-L3IRB interfaces irb unit 3891 family inet address ***********/30
set groups BVE-TEST-L3IRB bridge-domains vlan3891 routing-interface irb.3891
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB



















===== TTU =====

Customer: Baptist Health South Florida  - **********
Service: EPL
CPE: **************
EVC: 38.VLXP.056507..CBCL..
MVT: MVT2904056

-removed cpe from monitoring service
 ce-wpbiflle00w.cpe.fl.pompano.comcast.net 	************** 	Decommission 	MVT2904056 	7/26/2023 11:27 PM

-multiple services on MIAMFLFR04W





-unable to remove from MVT - another active service on port 2



















