
cadapass auth -a netengmguzma002 -s XgBJZ14mw2IuCYBD6O6Z7KLED9LJks00

jump cada --password-only --username=mguzma002 --env=prod null
eval $(ssh-agent)
ssh-add


================================
             ORDERS
================================

James - 2503 - 1004858832 - EDI - Y

EOD - 1004818826 - EDI - Y

EOD - 1003712418 - ENS - N

EOD - 1004696038 - EDI - N

EOD - 1004779810 - EDI - N


BW - 1005032834 - ENS
BW - 1005022932 - EDI













================================
SURVEYS - INFORMATION - NOT DONE
================================
Delan - 3012 - 2537597



Service Accepting
=================
TJ - 6155941811 - 130450086 - EDI information
Miguel - 5144482295 - 1004908930
Frank Martinez - 2392238390 - 939776748
Cori - 9126172089 - OSite_723387_1 - check speed ***************





================================
ORDERS TO CHECK TOMORROW
================================



Jira Tickets
=================



End Of Day Orders 
=================




MVT
===
OSite_593966_1





Disco
=====
1005023630
1005023632



BandWidth Upgrade
=================



Underlay
========















================================
STUDY - STUDY - STUDY - STUDY
================================















================================
	    UNDERLAY
================================

++++ TTU ++++

Customer: DREAM MARKET - OSite_680831_1 
Service: BVE
CPE: *************
UNI: port 2
VLAN: 3999

-created virtual interface and deleted after pinging
-p2p pinging on virtual interface
-enable GigabitEthernet0/0/0/5.3999

    

interface create ip-interface av-ref ip ************/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3999



set groups BVE-TEST-L3IRB interfaces irb unit 3990 family inet address ************/30
set groups BVE-TEST-L3IRB bridge-domains vlan3990 routing-interface irb.3990
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB







================================
	     OTHERS
================================

++++ TTU ++++

Customer: The Colony Hotel Palm Beach - **********
Service: EDI
CPE: ************** - PLBHFLIJ03W
UNI: Port 1
VLAN: 3808

Notes
=======

According to desing the CE should connect to sag12 on port GE-0/0/0/30
but there is no sfp slotted on port GE-0/0/0/30 - preconfig is loaded
order is rejected for no CE online and no SFP slotted at the HE




RP/0/RP0/CPU0:sag12.northpoint.fl.pompano#sh controll opt 0/0/0/30
Wed Sep 13 20:55:52.342 UTC

 Controller State: Down 

 Transport Admin State: In Service 

 Laser State: Off 

         Optics not present
         Optics Type: Unavailable
         DWDM Carrier Info: Unavailable, MSA ITU Channel= Unavailable, Frequency= Unavailable , Wavelength= Unavailable 
         TX Power = Unavailable 
         RX Power = Unavailable 




