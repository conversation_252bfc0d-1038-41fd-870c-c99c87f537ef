

================================
ACTIVATION
================================

MVT2915477
MVT2915507
MVT2921014
MVT2921149
MVT2922450
MVT2922695
MVT2922714
MVT2922721
MVT2923263
MVT2926800
MVT2927179
MVT2931986



1323508



End Of Day Orders
=================



MVT
===




Disco
=====
1004859030



BandWidth Upgrade
=================



Underlay
========




Service Accepting
=================
Joe
9418822202
OSite_699723_1



================================
             ORDERS
================================


Preston - activate
4169
1004706180
enable GigabitEthernet0/0/0/2.3564
enable Bundle-Ether1.3564
accepting


Jason - activate
1654
1004731844
enable ae107.1504
not accepting


Mike - activate
1459
1004676842
accept


Luis - 
1004573666








================================
SURVEYS - INFORMATION - NOT DONE
================================

Donald
2506381
no sfp slotted


Eral - survey
2405
osite_715880_1















================================
	    UNDERLAY
================================

++++ TTU ++++

Customer:  Cunningham Restaurant Group - 1004460506 
Service: BVE
CPE: ************** 
UNI: port 1
VLAN: 3976


-created ip-int and deleted after pinging

Pinging ************* with 32 bytes of data:                                                                            Reply from *************: bytes=32 time=102ms TTL=52                                                                    Reply from *************: bytes=32 time=75ms TTL=52                                                                     Reply from *************: bytes=32 time=96ms TTL=52                                                                     Reply from *************: bytes=32 time=116ms TTL=52 

interface create ip-interface av-ref ip *************/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3976



-loaded config on port 1


set groups BVE-TEST-L3IRB interfaces irb unit 3993 family inet address *************/30
set groups BVE-TEST-L3IRB bridge-domains vlan3993 routing-interface irb.3993
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB







================================
	     OTHERS
================================

++++ TTU ++++

Customer: The Colony Hotel Palm Beach - **********
Service: EDI
CPE: ************** - PLBHFLIJ03W
UNI: Port 1
VLAN: 3808

Notes
=======

According to desing the CE should connect to sag12 on port GE-0/0/0/30
but there is no sfp slotted on port GE-0/0/0/30 - preconfig is loaded
order is rejected for no CE online and no SFP slotted at the HE




RP/0/RP0/CPU0:sag12.northpoint.fl.pompano#sh controll opt 0/0/0/30
Wed Sep 13 20:55:52.342 UTC

 Controller State: Down 

 Transport Admin State: In Service 

 Laser State: Off 

         Optics not present
         Optics Type: Unavailable
         DWDM Carrier Info: Unavailable, MSA ITU Channel= Unavailable, Frequency= Unavailable , Wavelength= Unavailable 
         TX Power = Unavailable 
         RX Power = Unavailable 




