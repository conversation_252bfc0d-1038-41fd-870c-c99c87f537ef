

=======================
ACTIVATION
=======================

1004460482 - bve
1004460484 - bve
1004460492 - bve
1004460496 - bve
-
1004460498 - bve
1004460500 - bve
1004677186 - mvt
********** - mvt
-
1004685670 - disco



=======================



Balford - activate
3354
osite_682042_1
CT-17042
accepting


gio - activate
2293
1004568324
accepting


Mike - activate
2502
1004516312
accepting


Juan - activate
2007
1004472566
not accepting


Billy - activate
bweeks0940k
1004438148
closure failed
accepting


Will - activate
4724
1004400060
not accepting


Roger Wight - activate
1132
1004588796
not accepting


















================================
SURVEYS - INFORMATION - NOT DONE
================================


Charles - survey
2197
OSite_699831_1


Keith - 
2479066


Brian - customer
2486139509
21.vlxp.057825
1004666420













===== TTU =====

Customer: Cunningham Restaurant Group - 1004460500
Service: BVE
CPE: **************
UNI: 1
VLAN: 3654

-loaded config on port 1
-created ip-int and deleted after pinging
                                
Pinging ************ with 32 bytes of data:                                              
Reply from ************: bytes=32 time=64ms TTL=54                                       
Reply from ************: bytes=32 time=67ms TTL=54                                       
Reply from ************: bytes=32 time=65ms TTL=54                                       
Reply from ************: bytes=32 time=66ms TTL=54 



interface create ip-interface av-ref ip ************/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3654


set groups BVE-TEST-L3IRB interfaces irb unit 3985 family inet address ************/30
set groups BVE-TEST-L3IRB bridge-domains vlan3985 routing-interface irb.3985
set apply-groups BVE-TEST-L3IRB




















===== TTU =====

Customer: ENA-ME-IL-*********  - **********
Service: ENS
CPE: **************
EVC: 22.VLXM.009401..CBCL..

-removed cpe from monitoring service

ce-okfoilgr00w.cpe.il.chicago.comcast.net	**************	Decommission	MVT2898878	7/13/2023 10:42 PM







-unable to remove from MVT - another active service on port 2



















