
cadapass auth -a netengmguzma002 -s XgBJZ14mw2IuCYBD6O6Z7KLED9LJks00
eval $(ssh-agent)
ssh-add

jump cada --password-only --username=MGuzma002 --env=prod null

Y*LCAh(5dePEsEY5*_jpBaKmf7hKAf1a

29O739L6
================================
ACTIVATION
================================

MVT2915477
MVT2922695
MVT2923263


MVT2927179
MVT2931986
MVT2934080
MVT2937316
MVT2938968
MVT2948560 - fix ce-spfdilti00w.cpe.il.chicago.comcast.net - 96.204.226.6
MVT2950902 - fix




End Of Day Orders 
=================






MVT
===
OSite_642306_1
OSite_633965_1
OSite_573619_1 
OSite_643004_1
-
OSite_539563_1 
OSite_612938_1
OSite_647167_1 
OSite_529550_1
-
OSite_704914_1 
OSite_695354_1







Disco
=====
1004854082




BandWidth Upgrade
=================



Underlay
========




Service Accepting
=================
Marc
5179775784
1004711006


Becky - OFFNET - waiting
6309884139
1004136138









Jira Tickets
=================




================================
             ORDERS
================================


===== 9:30am - 10:30am cst ====

Mike - bve move
7722066539
37012202
190891


Ken
1511
1004166752
accepting

===============================


Mario
3327
1004602118
a


Euphrates
2198
1004898554
a


Travis
4051
1004663688
n











================================
SURVEYS - INFORMATION - NOT DONE
================================


Adam
3702
OSite_723007_1























================================
	    UNDERLAY
================================

++++ TTU ++++

Customer: DREAM MARKET - OSite_680831_1 
Service: BVE
CPE: *************
UNI: port 2
VLAN: 3999

-created virtual interface and deleted after pinging
-p2p pinging on virtual interface
-enable GigabitEthernet0/0/0/5.3999

    

interface create ip-interface av-ref ip ************/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3999



set groups BVE-TEST-L3IRB interfaces irb unit 3990 family inet address ************/30
set groups BVE-TEST-L3IRB bridge-domains vlan3990 routing-interface irb.3990
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB







================================
	     OTHERS
================================

++++ TTU ++++

Customer: The Colony Hotel Palm Beach - **********
Service: EDI
CPE: ************** - PLBHFLIJ03W
UNI: Port 1
VLAN: 3808

Notes
=======

According to desing the CE should connect to sag12 on port GE-0/0/0/30
but there is no sfp slotted on port GE-0/0/0/30 - preconfig is loaded
order is rejected for no CE online and no SFP slotted at the HE




RP/0/RP0/CPU0:sag12.northpoint.fl.pompano#sh controll opt 0/0/0/30
Wed Sep 13 20:55:52.342 UTC

 Controller State: Down 

 Transport Admin State: In Service 

 Laser State: Off 

         Optics not present
         Optics Type: Unavailable
         DWDM Carrier Info: Unavailable, MSA ITU Channel= Unavailable, Frequency= Unavailable , Wavelength= Unavailable 
         TX Power = Unavailable 
         RX Power = Unavailable 




