
cadapass auth -a netengmguzma002 -s XgBJZ14mw2IuCYBD6O6Z7KLED9LJks00https://ev01.b.comcast.net/menu.html

jump cada --password-only --username=mguzma002 --env=prod null
eval $(ssh-agent)
ssh-add


================================
             ORDERS
================================

Robert - 1004669824




Demetris - 5023 - 1004844478 - EDI - Y

Maurice - 2651 - OSite_721103_1 - EDI - Y

Thony - 1522 - 37558804 - 194051 - BVE

Mike - 1269 - 1004862278 - EDI - Y

EOD - 1004891042 - EVPL - N

Josh - 5076 - 37522515 - 193803 - BVE













================================
SURVEYS - INFORMATION - NOT DONE
================================
Alex - 1499 - 130541858
Eric Andrew - 2052180713 - 1004829670



Service Accepting
=================






================================
OTHER ORDERS COMPLETED
================================







Enthusiastic Network Engineer 2 at Comcast's Test and Turn Up Central Division, seeking to leverage extensive experience in assisting field technicians and customers. Dedicated to providing exceptional customer service through expert troubleshooting and efficient activation support. Aiming to contribute my technical skills and customer-centric approach to enhance network operations and customer satisfaction.

Mentored new hires in network engineering principles and company-specific protocols, enhancing team efficiency and knowledge sharing




























================================
	    UNDERLAY
================================

++++ TTU ++++

Customer: DREAM MARKET - OSite_680831_1 
Service: BVE
CPE: *************
UNI: port 2
VLAN: 3999

-created virtual interface and deleted after pinging
-p2p pinging on virtual interface
-enable GigabitEthernet0/0/0/5.3999

    

interface create ip-interface av-ref ip ***********/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3760



set groups BVE-TEST-L3IRB interfaces irb unit 3990 family inet address ************/30
set groups BVE-TEST-L3IRB bridge-domains vlan3990 routing-interface irb.3990
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB







================================
	     OTHERS
================================

++++ TTU ++++

Customer: The Colony Hotel Palm Beach - **********
Service: EDI
CPE: ************** - PLBHFLIJ03W
UNI: Port 1
VLAN: 3808

Notes
=======

According to desing the CE should connect to sag12 on port GE-0/0/0/30
but there is no sfp slotted on port GE-0/0/0/30 - preconfig is loaded
order is rejected for no CE online and no SFP slotted at the HE




RP/0/RP0/CPU0:sag12.northpoint.fl.pompano#sh controll opt 0/0/0/30
Wed Sep 13 20:55:52.342 UTC

 Controller State: Down 

 Transport Admin State: In Service 

 Laser State: Off 

         Optics not present
         Optics Type: Unavailable
         DWDM Carrier Info: Unavailable, MSA ITU Channel= Unavailable, Frequency= Unavailable , Wavelength= Unavailable 
         TX Power = Unavailable 
         RX Power = Unavailable 




