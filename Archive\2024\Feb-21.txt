

cadapass auth -a netengmguzma002 -s XgBJZ14mw2IuCYBD6O6Z7KLED9LJks00https://ev01.b.comcast.net/menu.html

jump cada --password-only --username=mguzma002 --env=prod null
eval $(ssh-agent)
ssh-add


<PERSON> - 4109
<PERSON> - 2910
Dan <PERSON> - 4067
Dan Ball - 3640
Will -
Aaron - 5075
Brad <PERSON> - 4733




================================
             ORDERS
================================

EOD - 1004975668 - https://tpx.sys.comcast.net/browse/CT-22571  waiting on PM to complete

Rick - 1004983894 - test - complete order ************

Dan - 4067 - 1004840618 EDI - waiting on task to be ready complete in century

1004839084 - work this tomorrow

-

Brian - 3217 - 1004944312 - ENS

Jeff - 3104 - 1004375802 EDI N

Matt - 4109 - 1004752808 ENS Y

Vince - 3021 - 1003712420 ENS N

Tremayne - 1819 - 1004929572 ENS Y

Lorenzo - 2725 - 1004876876 ENS N

Matt - 4109 - 1004752786 ENS Y

Adam - 2569240290 - 37513858 BVE OTT

Matt - 4109 1004752834 ENS Y

Ryan - 5132 - 1004981014 EDI Y

EOD - 1004784414

EOD - 1004931944

EOD - 1004852816 ----- CHeck tomorrow *************************










================================
SURVEYS - INFORMATION - NOT DONE
================================
Terran - 9546629254 - 37620882 - 8495600204566014 BVE OTT ********** wait on PM confirmation order is not ready 
Ray - 2480217

Service Accepting
=================
Julio - 4075675055 - 1004777260




================================
OTHER ORDERS COMPLETED
================================


















================================
	    UNDERLAY
================================

++++ TTU ++++

Customer: DREAM MARKET - OSite_680831_1 
Service: BVE
CPE: *************
UNI: port 2
VLAN: 3999

-created virtual interface and deleted after pinging
-p2p pinging on virtual interface
-enable GigabitEthernet0/0/0/5.3999

    

interface create ip-interface av-ref ip ***********/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3760



set groups BVE-TEST-L3IRB interfaces irb unit 3990 family inet address ************/30
set groups BVE-TEST-L3IRB bridge-domains vlan3990 routing-interface irb.3990
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB







================================
	     OTHERS
================================

++++ TTU ++++

Customer: The Colony Hotel Palm Beach - **********
Service: EDI
CPE: ************** - PLBHFLIJ03W
UNI: Port 1
VLAN: 3808

Notes
=======

According to desing the CE should connect to sag12 on port GE-0/0/0/30
but there is no sfp slotted on port GE-0/0/0/30 - preconfig is loaded
order is rejected for no CE online and no SFP slotted at the HE




RP/0/RP0/CPU0:sag12.northpoint.fl.pompano#sh controll opt 0/0/0/30
Wed Sep 13 20:55:52.342 UTC

 Controller State: Down 

 Transport Admin State: In Service 

 Laser State: Off 

         Optics not present
         Optics Type: Unavailable
         DWDM Carrier Info: Unavailable, MSA ITU Channel= Unavailable, Frequency= Unavailable , Wavelength= Unavailable 
         TX Power = Unavailable 
         RX Power = Unavailable 




