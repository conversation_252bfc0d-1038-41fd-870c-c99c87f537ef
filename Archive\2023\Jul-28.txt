

=======================
ACTIVATION
=======================

MVT2905017


1004717970 - bve




Dan - activate - fix orion issue
4067
OSite_676241_1


A<PERSON> - customer
7346794775
1004189906
accepting

================

Kevin - bve
4088
35723897
181909


Therron - bve
6345
35780201
182612


Jeff - activate
5113
2466089
1004540362
enable Bundle-Ether1.3929
enable Bundle-Ether1.3932
accepted












================================
SURVEYS - INFORMATION - NOT DONE
================================

ufredies - 
2198
130323741
enable GigabitEthernet0/0/0/31.3877
enable Bundle-Ether1.3877
enable ae109.3877



Ricardo - waiting
1652
400 SW 2ND ST
1004157558












===== TTU =====

Customer: CodeMap - 1004717970
Service: BVE
CPE: *************
UNI: 2
VLAN: 3796

-loaded config on port 1
-created ip-int and deleted after pinging
                                
Reply from ***********: bytes=32 time=2833ms
TTL=54                                                               
Reply from ***********: bytes=32 time=56ms TTL=54                                                                       
Reply from ***********: bytes=32 time=61ms TTL=54                                                                       
Reply from ***********: bytes=32 time=57ms TTL=54  



interface create ip-interface av-ref ip ***********/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3796


set groups BVE-TEST-L3IRB interfaces irb unit 3903 family inet address *************/30
set groups BVE-TEST-L3IRB bridge-domains vlan3903 routing-interface irb.3903
set apply-groups BVE-TEST-L3IRB




















===== TTU =====

Customer: Baptist Health South Florida  - **********
Service: EPL
CPE: **************
EVC: 38.VLXP.056507..CBCL..
MVT: MVT2904056

-removed cpe from monitoring service
 ce-wpbiflle00w.cpe.fl.pompano.comcast.net 	************** 	Decommission 	MVT2904056 	7/26/2023 11:27 PM

-multiple services on MIAMFLFR04W





-unable to remove from MVT - another active service on port 2



















