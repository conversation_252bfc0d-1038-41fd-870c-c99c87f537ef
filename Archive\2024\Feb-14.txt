
cadapass auth -a netengmguzma002 -s XgBJZ14mw2IuCYBD6O6Z7KLED9LJks00https://ev01.b.comcast.net/menu.html

jump cada --password-only --username=mguzma002 --env=prod null
eval $(ssh-agent)
ssh-add


<PERSON> - 4109
<PERSON> - 2910
Dan De<PERSON>hr<PERSON>ofaro - 4067
Dan Ball - 3640

================================
             ORDERS
================================

Juan - 2007 - OSite_721174_1 - EDI - complete in orion - disco first OSite_634123_1

EOD - 1004975668 - https://tpx.sys.comcast.net/browse/CT-22571  waiting on PM to complete


Gio - 2293 - 1004869098 EDI Y

Eric - 5067 - 1004927304 EDI Y

Matt - 4109 - 1004752822 ENS Y

Gio - 2293 - 1004952870 EDI N

Ricardo - 1652 - OSite_734404_1 EDI Y











================================
SURVEYS - INFORMATION - NOT DONE
================================
Charlie - 1765 - 1004878060



Service Accepting
=================
Kyle - 2623371339 - OSite_735162_1
Hopper - 1004862792 - 30.krgs.073792




================================
OTHER ORDERS COMPLETED
================================
BW - 1005053942
BW - 1005061244





















================================
	    UNDERLAY
================================

++++ TTU ++++

Customer: DREAM MARKET - OSite_680831_1 
Service: BVE
CPE: *************
UNI: port 2
VLAN: 3999

-created virtual interface and deleted after pinging
-p2p pinging on virtual interface
-enable GigabitEthernet0/0/0/5.3999

    

interface create ip-interface av-ref ip ***********/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3760



set groups BVE-TEST-L3IRB interfaces irb unit 3990 family inet address ************/30
set groups BVE-TEST-L3IRB bridge-domains vlan3990 routing-interface irb.3990
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB







================================
	     OTHERS
================================

++++ TTU ++++

Customer: The Colony Hotel Palm Beach - **********
Service: EDI
CPE: ************** - PLBHFLIJ03W
UNI: Port 1
VLAN: 3808

Notes
=======

According to desing the CE should connect to sag12 on port GE-0/0/0/30
but there is no sfp slotted on port GE-0/0/0/30 - preconfig is loaded
order is rejected for no CE online and no SFP slotted at the HE




RP/0/RP0/CPU0:sag12.northpoint.fl.pompano#sh controll opt 0/0/0/30
Wed Sep 13 20:55:52.342 UTC

 Controller State: Down 

 Transport Admin State: In Service 

 Laser State: Off 

         Optics not present
         Optics Type: Unavailable
         DWDM Carrier Info: Unavailable, MSA ITU Channel= Unavailable, Frequency= Unavailable , Wavelength= Unavailable 
         TX Power = Unavailable 
         RX Power = Unavailable 




