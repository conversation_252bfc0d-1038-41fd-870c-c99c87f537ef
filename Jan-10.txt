
===============================
CADA
===============================
jump cada --password-only --username=mguzma002 --env=16char null
eval $(ssh-agent)
ssh-add


===============================
Update State of Illinois orders
===============================
https://comcastcorp.sharepoint.com/:x:/r/sites/cdCDServiceOne/_layouts/15/Doc.aspx?sourcedoc=%7BC3296CF3-AEA3-4572-BF03-40AC166F567F%7D&file=State%20of%20%20Illinois%20Weekly%20Updates%20-2024.xlsx&action=default&mobileredirect=true

\\cable\sccm-dfs\EntMEMDFS\Source\

eg-chchilfo07w.icn.il.chicago.comcast.net - Test Port is on xe-1/0/0
160 N LASALLE ST,,CHICAGO,IL 60601 - et-8/1/0

===============================
TECH LIST
===============================
Matt Foster - 4109 6158643541
Robert Keys - 2910
Dan DeChristofaro - 4067
Dan Ball - 3640
Will Salazar - 3751
Aaron - 5075
Brad Harvey - 4733
Travis Ower - 4051
Christ Chong - 2041
Sadoc - 2730
Alex - 1499
Will Wesley 5095
Paul Kirk 2425
Mike Daniel 1459Vise Partners LLC
Mike Miami Metro 2427
Shane Kelly 4138
Jeff Davis 3104
Luis 2333 hialeah
Roger Wight 1132 
Keith Sydnor 5732
Andy  Dzielawa 3320
Anthony Moore 3322
Yolanda 2025
Robert Geiske 2910
Craig Ridley 2679
Ray Furman 3286 
john Young 4037 
John Riddle 3611
Donald 1204
Larry miami 2217
Farid 1032
Francisco Taylor 2532
Thony 1522
Andrew Gross 4020
morris 3318
Wilbert 3103
justin 4216
angel 1325


================================
             ORDERS
================================

ernesto 1005354596 - jira 1005354596 - fix fqdn **********************

Ben 2307 130694332 OSite_765227_1 -  INC007373216

rian 3359 - 40771902 BVE

sadoc 130964186 OSite_848826_1 EDI

belaford 3354 - 1005465346 ENS

brad 4733 130829330 OSite_807664_1 - complete later waiting on port to be updated  **************************

ricardo 1005793788 ENS - fix site collision *******************

juan OSite_850869_1 EDI




inumpp102.indianapolis.in.indiana.comcast.net
INBGD00W0B

ac:db:48:ad:47:cd


AC:DB:48:AD:47:CD
78:D7:1A:67:C0:44




Jan  9 18:26:50  re0-sur02.margate.fl.pompano rpd[8454]: RPD_L2VPN_REMOTE_SITE_COLLISION: Two remote PEs (RDs **************:649: and ************:208:) have the same site ID (1) in VPN ELAN-ACCT933714900-**********








as16	BVE	mguzma002_ufo96
as10	BVE	mguzma002_ygg60
as18	BVE	mguzma002_kzc88
as07	BVE	mguzma002_qgy03
as15	BVE	mguzma002_sjc95
as19	BVE	mguzma002_rzt20
as17	BVE	mguzma002_ziu53
as11	BVE	mguzma002_dlv19
as12	BVE	mguzma002_mxy65














********* eohfc check tomorrow 1005067544 - CT-30310

Modem CLLI: CHTPSCGL00W
Modem MAC: f8790a983bef
Nid CLLI: CHTPSCGL01W
Nid MAC: ac89d2e43500(f)
Nid IP: *************
































remove taxes by march 1
march 14 - fat bonus





tracerotue mpls lsp <destination-address>
ping mpls lsp <destination-address>
show route table inet.3 <remote-loopback-or-PE-IP>











================================
SURVEYS - INFORMATION - NOT DONE
================================
craig 6152532983 - 2656723


===============================
Service Accepting
================================

Sandra OSite_838742_1 6067390403



================================
OTHER ORDERS COMPLETED
================================






================================
	    UNDERLAY
================================

++++ TTU ++++

Customer: DREAM MARKET - OSite_680831_1 
Service: BVE
CPE: *************
UNI: port 2
VLAN: 3999

-created virtual interface and deleted after pinging
-p2p pinging on virtual interface
-enable GigabitEthernet0/0/0/5.3999

    

interface create ip-interface av-ref ip ***********/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3992



set groups TEST-L3IRB interfaces irb unit 3990 family inet address ************/30
set groups TEST-L3IRB bridge-domains vlan3990 routing-interface irb.3990
set apply-groups TEST-L3IRB
commit



delete apply-groups TEST-L3IRB
delete groups TEST-L3IRB







================================
	     OTHERS
================================

++++ TTU ++++

Customer: The Colony Hotel Palm Beach - **********
Service: EDI
CPE: ************** - PLBHFLIJ03W
UNI: Port 1
VLAN: 3808

Notes
=======

According to desing the CE should connect to sag12 on port GE-0/0/0/30
but there is no sfp slotted on port GE-0/0/0/30 - preconfig is loaded
order is rejected for no CE online and no SFP slotted at the HE




RP/0/RP0/CPU0:sag12.northpoint.fl.pompano#sh controll opt 0/0/0/30
Wed Sep 13 20:55:52.342 UTC

 Controller State: Down 

 Transport Admin State: In Service 

 Laser State: Off 

         Optics not present
         Optics Type: Unavailable
         DWDM Carrier Info: Unavailable, MSA ITU Channel= Unavailable, Frequency= Unavailable , Wavelength= Unavailable 
         TX Power = Unavailable 
         RX Power = Unavailable 





