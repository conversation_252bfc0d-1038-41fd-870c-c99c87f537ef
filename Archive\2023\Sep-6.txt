

================================
ACTIVATION
================================

MVT2915477
MVT2915507
MVT2917458
MVT2920546

1003466942 - disco
1004557832 - disco
1004747626

OSite_721831_1 - bve
OSite_693876_1 - bve




Jeff - customer
6785589261
OSite_715404_1


================================

Charlie - activate
4367
1004354662
accepting


Gio - activate
2293
OSite_705788_1
accepting


Gio - activate
2293
1004325332
not accepting


Jean - activate
4602
1004622294
accepting


Gilbert - activate
4158
OSite_680043_1
enable Bundle-Ether1.3965
enable GigabitEthernet0/0/0/23.3965
accepting


Rodderick  - activate
4100
2485429
enable GigabitEthernet0/0/0/14.3847
enable Bundle-Ether1.3847
accepting


Matt - activate
2378
OSite_703372_1
accepting


END OF SHIFT ORDER
1004483448






















================================
SURVEYS - INFORMATION - NOT DONE
================================



Dean - survey
2359
osite_685716_1


Roger - bve
1132
OSite_721831_1


Chris - survey
1004545432









===== TTU =====

Customer: Rutherford Farmers Co Op - OSite_693876_1
Service: BVE
CPE: **************
UNI: 1
VLAN: 3838

-loaded config on port 1
-created ip-int and deleted after pinging
-enable GigabitEthernet0/0/0/29.3838
-enable Bundle-Ether1.3838
-match negotiation

Pinging *********** with 32 bytes of data:                                            
Reply from ***********: bytes=32 time=69ms TTL=51                                     
Reply from ***********: bytes=32 time=75ms TTL=51                                     
Reply from ***********: bytes=32 time=68ms TTL=51                                     
Reply from ***********: bytes=32 time=69ms TTL=51 

interface create ip-interface av-ref ip ***********/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3838


set groups BVE-TEST-L3IRB interfaces irb unit 3891 family inet address ***********/30
set groups BVE-TEST-L3IRB bridge-domains vlan3891 routing-interface irb.3891
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB







===== TTU =====

Customer: Midlothian School District 143 - **********
Service: BVE
CPE: **************
UNI: 1
VLAN: 3939

Already traffic on port 1 - edge already connected and reachable








===== TTU =====

Customer: Atos IT Solutions & Services - **********
Service: EPL
CPE: *************
EVC: 30.VLXP.942560..CBCL..
MVT: MVT2920287

-removed cpe from monitoring service





-multiple services on MIAMFLFR04W





-unable to remove from MVT - another active service on port 2



















