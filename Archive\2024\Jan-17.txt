
cadapass auth -a netengmguzma002 -s XgBJZ14mw2IuCYBD6O6Z7KLED9LJks00

jump cada --password-only --username=mguzma002 --env=prod null
eval $(ssh-agent)
ssh-add


================================
ACTIVATION
================================

MVT2915477
MVT2922695
MVT2923263
MVT2927179


MVT2931986
MVT2934080
MVT2937316
MVT2938968
MVT2948560 - fix ce-spfdilti00w.cpe.il.chicago.comcast.net - 96.204.226.6
MVT2950902 - fix




End Of Day Orders 
=================




MVT
===



Disco
=====




BandWidth Upgrade
=================



Underlay
========



Service Accepting
=================
1004723258
1004766680
1004884240
1004843522
-



Jira Tickets
=================




================================
             ORDERS
================================
 MVT2972180 - 
MVT2972761 - fix fqdn


Cisco - 2532 - OSite_709596_1 - bad light levels

==========

Gio - 2293 - 36613059/131629 - Transport ChangE BVE

John - 3611 - 1004690320 - ENS

Jared - 3032 - 1004887354 - EDI

Josh - 3406 - 1004676176 - EVPL

Juan - 2007 - OSite_698318_1 - EDI

Chris - 2041 - 37293797/192762 - BVE

Juan - 2007 - OSite_699549_1 - EDI




output VPLS-PHY10G-8-PREMIUM-8-PRIORITY-110000-BASIC-TX

show route table ELAN-ACCT931094104-VRF3000312.l2vpn.0

================================
ORDERS TO CHECK TOMORROW
================================




================================
SURVEYS - INFORMATION - NOT DONE
================================
Jim - 4794 - 1004898454 - EPL - waiting




================================
STUDY - STUDY - STUDY - STUDY
================================















================================
	    UNDERLAY
================================

++++ TTU ++++

Customer: DREAM MARKET - OSite_680831_1 
Service: BVE
CPE: *************
UNI: port 2
VLAN: 3999

-created virtual interface and deleted after pinging
-p2p pinging on virtual interface
-enable GigabitEthernet0/0/0/5.3999

    

interface create ip-interface av-ref ip ************/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3999



set groups BVE-TEST-L3IRB interfaces irb unit 3990 family inet address ************/30
set groups BVE-TEST-L3IRB bridge-domains vlan3990 routing-interface irb.3990
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB







================================
	     OTHERS
================================

++++ TTU ++++

Customer: The Colony Hotel Palm Beach - **********
Service: EDI
CPE: ************** - PLBHFLIJ03W
UNI: Port 1
VLAN: 3808

Notes
=======

According to desing the CE should connect to sag12 on port GE-0/0/0/30
but there is no sfp slotted on port GE-0/0/0/30 - preconfig is loaded
order is rejected for no CE online and no SFP slotted at the HE




RP/0/RP0/CPU0:sag12.northpoint.fl.pompano#sh controll opt 0/0/0/30
Wed Sep 13 20:55:52.342 UTC

 Controller State: Down 

 Transport Admin State: In Service 

 Laser State: Off 

         Optics not present
         Optics Type: Unavailable
         DWDM Carrier Info: Unavailable, MSA ITU Channel= Unavailable, Frequency= Unavailable , Wavelength= Unavailable 
         TX Power = Unavailable 
         RX Power = Unavailable 




