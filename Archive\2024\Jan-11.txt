
cadapass auth -a netengmguzma002 -s XgBJZ14mw2IuCYBD6O6Z7KLED9LJks00

jump cada --password-only --username=mguzma002 --env=prod null
eval $(ssh-agent)
ssh-add


================================
ACTIVATION
================================

MVT2915477
MVT2922695
MVT2923263
MVT2927179


MVT2931986
MVT2934080
MVT2937316
MVT2938968
MVT2948560 - fix ce-spfdilti00w.cpe.il.chicago.comcast.net - 96.204.226.6
MVT2950902 - fix




End Of Day Orders 
=================




MVT
===



Disco
=====




BandWidth Upgrade
=================



Underlay
========
1004894970 - BVE
1004520126 - BVE
1004798868 - BVE


Service Accepting
=================
Hasin - 2510278




Jira Tickets
=================



================================
             ORDERS
================================


Sadoc - 2732 - 1004863318 - ENS add notes

Gio - 2293 - 1004732938 - EDI

Juan - 2007 - 1004595116 - EDI

Dan - 4067 - 1004971286 - EDI

Cisco - 2532 - OSite_731248_1 EDI

Cisco - 2532 - OSite_713869_1 - EDI 




EOD - OSite_550600_1 - customer with live traffic
EOD - OSite_706455_1 - order already tested




















================================
ORDERS TO CHECK TOMORROW
================================




================================
SURVEYS - INFORMATION - NOT DONE
================================
Jim - 4794 - 1004898454 - EPL - waiting




================================
STUDY - STUDY - STUDY - STUDY
================================



benchmark reflector delete name refDefault all-test-instances
benchmark delete name refDefault
benchmark create generator name EPL-3926-1 port 5
benchmark generator set name EPL-3926-1 mode out-of-service
benchmark profile configuration set name EPL-3926-1 bandwidth 9300
benchmark test create name EPL-3926-3 profile EPL-3926-3 dst-mac 00:18:63:00:0c:40
benchmark enable
benchmark generator enable
benchmark test enable name EPL-3926-3
benchmark test start name EPL-3926-3


benchmark test disable name EPL-3926-3
benchmark generator disable
benchmark disable



benchmark test show name EPL-3926-3 status











================================
	    UNDERLAY
================================

++++ TTU ++++

Customer: DREAM MARKET - OSite_680831_1 
Service: BVE
CPE: *************
UNI: port 2
VLAN: 3999

-created virtual interface and deleted after pinging
-p2p pinging on virtual interface
-enable GigabitEthernet0/0/0/5.3999

    

interface create ip-interface av-ref ip ************/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3999



set groups BVE-TEST-L3IRB interfaces irb unit 3990 family inet address ************/30
set groups BVE-TEST-L3IRB bridge-domains vlan3990 routing-interface irb.3990
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB







================================
	     OTHERS
================================

++++ TTU ++++

Customer: The Colony Hotel Palm Beach - **********
Service: EDI
CPE: ************** - PLBHFLIJ03W
UNI: Port 1
VLAN: 3808

Notes
=======

According to desing the CE should connect to sag12 on port GE-0/0/0/30
but there is no sfp slotted on port GE-0/0/0/30 - preconfig is loaded
order is rejected for no CE online and no SFP slotted at the HE




RP/0/RP0/CPU0:sag12.northpoint.fl.pompano#sh controll opt 0/0/0/30
Wed Sep 13 20:55:52.342 UTC

 Controller State: Down 

 Transport Admin State: In Service 

 Laser State: Off 

         Optics not present
         Optics Type: Unavailable
         DWDM Carrier Info: Unavailable, MSA ITU Channel= Unavailable, Frequency= Unavailable , Wavelength= Unavailable 
         TX Power = Unavailable 
         RX Power = Unavailable 




