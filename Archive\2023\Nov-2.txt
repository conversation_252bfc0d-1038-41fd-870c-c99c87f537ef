
cadapass auth -a netengmguzma002 -s XgBJZ14mw2IuCYBD6O6Z7KLED9LJks00

================================
ACTIVATION
================================

MVT2915477
MVT2915507
MVT2921014
MVT2921149
MVT2922450
MVT2922695
MVT2922714
MVT2922721
MVT2923263
MVT2926800
MVT2927179
MVT2931986
MVT2934080
MVT2937316
MVT2938968
MVT2938986



my ticket SR019361257


End Of Day Orders
=================



MVT
===
1004770270
1004848082 - ce-atlagabm08w.cpe.ga.atlanta.comcast.net - MVT2943930
1004848084
1004833574
-
1004816776
1004172662
1004629616
1004666550
-
1004655688
1004183308
1004183222
1004859932
-
1004818442
1004857732
1004834896
1004834894




Disco
=====





BandWidth Upgrade
=================







Underlay
========
1004850490 - waiting




Service Accepting
=================
Hasin - complete in century
3057757609
1004354674




Jira Tickets
=================
CT-20325






================================
             ORDERS
================================



Travis
4051
1004359206
enable Bundle-Ether1.3965
enable GigabitEthernet0/0/0/3.3965
enable Bundle-Ether139.3965
n



















================================
SURVEYS - INFORMATION - NOT DONE
================================


Widney 
8167871560
1004739610












================================
	    UNDERLAY
================================

++++ TTU ++++

Customer:  Automation Controls & Engineering - 1004850490 
Service: BVE
CPE:  ***************
UNI: port 1
VLAN: 3855


-created ip-int and deleted after pinging

Pinging *************** with 32 bytes of data:                                                                          Reply from ***************: bytes=32 time=60ms TTL=50                                                                   Reply from ***************: bytes=32 time=61ms TTL=50                                                                   Reply from ***************: bytes=32 time=82ms TTL=50                                                                   Reply from ***************: bytes=32 time=58ms TTL=50     

interface create ip-interface av-ref ip ***************/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3855


-loaded config on port 1


set groups BVE-TEST-L3IRB interfaces irb unit 3993 family inet address *************/30
set groups BVE-TEST-L3IRB bridge-domains vlan3993 routing-interface irb.3993
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB







================================
	     OTHERS
================================

++++ TTU ++++

Customer: The Colony Hotel Palm Beach - **********
Service: EDI
CPE: ************** - PLBHFLIJ03W
UNI: Port 1
VLAN: 3808

Notes
=======

According to desing the CE should connect to sag12 on port GE-0/0/0/30
but there is no sfp slotted on port GE-0/0/0/30 - preconfig is loaded
order is rejected for no CE online and no SFP slotted at the HE




RP/0/RP0/CPU0:sag12.northpoint.fl.pompano#sh controll opt 0/0/0/30
Wed Sep 13 20:55:52.342 UTC

 Controller State: Down 

 Transport Admin State: In Service 

 Laser State: Off 

         Optics not present
         Optics Type: Unavailable
         DWDM Carrier Info: Unavailable, MSA ITU Channel= Unavailable, Frequency= Unavailable , Wavelength= Unavailable 
         TX Power = Unavailable 
         RX Power = Unavailable 




