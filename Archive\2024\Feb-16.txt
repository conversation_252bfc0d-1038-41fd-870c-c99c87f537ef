

cadapass auth -a netengmguzma002 -s XgBJZ14mw2IuCYBD6O6Z7KLED9LJks00https://ev01.b.comcast.net/menu.html

jump cada --password-only --username=mguzma002 --env=prod null
eval $(ssh-agent)
ssh-add


<PERSON> - 4109
<PERSON> - 2910
Dan <PERSON>ofaro - 4067
Dan Ball - 3640
Will -

================================
             ORDERS
================================

Juan - 2007 - OSite_721174_1 - EDI - complete in orion - disco first OSite_634123_1

EOD - 1004975668 - https://tpx.sys.comcast.net/browse/CT-22571  waiting on PM to complete

Brian - 3217 - 1004944312 - ENS - complete tomorrow ********************************

Larry - 2217 - 37620024 - BVE

Brian - 4206 - 37664734 - B<PERSON> - 4100 - 1004752804 - test later

Charlie - 1765 - 1005036166 EDI








================================
SURVEYS - INFORMATION - NOT DONE
================================
Wilson - 5095 - 2570044 - nothing
Marvin - 1451 - 37154866 BVE - not done


Service Accepting
=================





================================
OTHER ORDERS COMPLETED
================================
BW - 1005061244
disco - 1004764652
disco - 1004764654
disco - 1005039440
disco - 1005039438 - done check later
disco - 1004773084 

















================================
	    UNDERLAY
================================

++++ TTU ++++

Customer: DREAM MARKET - OSite_680831_1 
Service: BVE
CPE: *************
UNI: port 2
VLAN: 3999

-created virtual interface and deleted after pinging
-p2p pinging on virtual interface
-enable GigabitEthernet0/0/0/5.3999

    

interface create ip-interface av-ref ip ***********/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3760



set groups BVE-TEST-L3IRB interfaces irb unit 3990 family inet address ************/30
set groups BVE-TEST-L3IRB bridge-domains vlan3990 routing-interface irb.3990
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB







================================
	     OTHERS
================================

++++ TTU ++++

Customer: The Colony Hotel Palm Beach - **********
Service: EDI
CPE: ************** - PLBHFLIJ03W
UNI: Port 1
VLAN: 3808

Notes
=======

According to desing the CE should connect to sag12 on port GE-0/0/0/30
but there is no sfp slotted on port GE-0/0/0/30 - preconfig is loaded
order is rejected for no CE online and no SFP slotted at the HE




RP/0/RP0/CPU0:sag12.northpoint.fl.pompano#sh controll opt 0/0/0/30
Wed Sep 13 20:55:52.342 UTC

 Controller State: Down 

 Transport Admin State: In Service 

 Laser State: Off 

         Optics not present
         Optics Type: Unavailable
         DWDM Carrier Info: Unavailable, MSA ITU Channel= Unavailable, Frequency= Unavailable , Wavelength= Unavailable 
         TX Power = Unavailable 
         RX Power = Unavailable 




