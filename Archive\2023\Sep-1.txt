

================================
ACTIVATION
================================

MVT2915477
MVT2915507
MVT2917458

OSite_715688_1 - bve



<PERSON> - customer
2483963248
1004676832
accepting

================================



Travis - activate 2 order
4051
1004597848 - EDI
1004597842 - EPL
enable GigabitEthernet0/0/0/8.3859
not accepting


Ivan - activate
2610
1004707112
accept


Yolanda - bve
2025
OSite_715688_1
bedrock - comm-24582


Travis - activate
4051
1004529630
enable Bundle-Ether1.3989
enable Bundle-Ether1.3985
not accepting



















================================
SURVEYS - INFORMATION - NOT DONE
================================



















===== TTU =====

Customer: Village of Maywood - **********
Service: BVE
CPE: **************
UNI: 1
VLAN: 3977

-loaded config on port 1
-created ip-int and deleted after pinging

Pinging ************ with 32 bytes of data:                                                         Reply from ************: bytes=32 time=57ms TTL=54                                                  Reply from ************: bytes=32 time=60ms TTL=54                                                  Reply from ************: bytes=32 time=59ms TTL=54                                                  Reply from ************: bytes=32 time=61ms TTL=54  


interface create ip-interface av-ref ip ***********/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3977


set groups BVE-TEST-L3IRB interfaces irb unit 3891 family inet address ***********/30
set groups BVE-TEST-L3IRB bridge-domains vlan3891 routing-interface irb.3891
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB







===== TTU =====

Customer: Village of Maywood - **********
Service: BVE
CPE: **************
UNI: 1
VLAN: 3939

Already traffic on port 1 - edge already connected and reachable








===== TTU =====

Customer: Marquee Insurance Group  - **********
Service: EDI
CPE: **************
EVC: 30.VLXP.075654..CBCL..
MVT: MVT2917327

-removed cpe from monitoring service
ce-rswlgaiw03w.cpe.ga.atlanta.comcast.net 	************** 	Decommission 	MVT2917327 	8/28/2023 4:40 PM



-multiple services on MIAMFLFR04W





-unable to remove from MVT - another active service on port 2



















