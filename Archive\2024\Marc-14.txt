

cadapass auth -a netengmguzma002 -s XgBJZ14mw2IuCYBD6O6Z7KLED9LJks00https://ev01.b.comcast.net/menu.html

jump cada --password-only --username=mguzma002 --env=prod null
eval $(ssh-agent)
ssh-add


<PERSON> - 4109
<PERSON> - 2910
Dan <PERSON>aro - 4067
Dan Ball - 3640
Will <PERSON> - 3751
Aaron - 5075
<PERSON> - 4733
Travis Ower - 4051
Christ Chong - 2041
Sadoc - 2730
Alex - 1499
Will <PERSON> 5095
Paul Kirk 2425
Mike Daniel 1459
Shane <PERSON> 4138
Jeff <PERSON> 3104

================================
             ORDERS
================================

Jaime - 1162 1004210092 customer accepting - complete order when task is ready
EDO 1004919772 waiting on sfp to be slotted on port 4
EOD 1004761030 waiting on PM old CA has not been removed - PM was waiting on activecore to be canceled
-
work day - side jobs gigs
g.8032 protective ring


Tremayne 1819 1003984646 EDI Y

Shane 4138 OSite_720113_1 EDI Y

Gio 2293 1004072290 EDI N 

Jeff 3104 1004885264 EDI Y

Adam 3702 1004936592 EDI 

Juan 2007 1004685482 EDI Y

EOD 1004917456












================================
SURVEYS - INFORMATION - NOT DONE
================================




Service Accepting
=================
Patrick 8123270248 OSite_558956_1



================================
OTHER ORDERS COMPLETED
================================





================================
	    UNDERLAY
================================

++++ TTU ++++

Customer: DREAM MARKET - OSite_680831_1 
Service: BVE
CPE: *************
UNI: port 2
VLAN: 3999

-created virtual interface and deleted after pinging
-p2p pinging on virtual interface
-enable GigabitEthernet0/0/0/5.3999

    

interface create ip-interface av-ref ip ***********/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3760



set groups BVE-TEST-L3IRB interfaces irb unit 3990 family inet address ************/30
set groups BVE-TEST-L3IRB bridge-domains vlan3990 routing-interface irb.3990
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB







================================
	     OTHERS
================================

++++ TTU ++++

Customer: The Colony Hotel Palm Beach - **********
Service: EDI
CPE: ************** - PLBHFLIJ03W
UNI: Port 1
VLAN: 3808

Notes
=======

According to desing the CE should connect to sag12 on port GE-0/0/0/30
but there is no sfp slotted on port GE-0/0/0/30 - preconfig is loaded
order is rejected for no CE online and no SFP slotted at the HE




RP/0/RP0/CPU0:sag12.northpoint.fl.pompano#sh controll opt 0/0/0/30
Wed Sep 13 20:55:52.342 UTC

 Controller State: Down 

 Transport Admin State: In Service 

 Laser State: Off 

         Optics not present
         Optics Type: Unavailable
         DWDM Carrier Info: Unavailable, MSA ITU Channel= Unavailable, Frequency= Unavailable , Wavelength= Unavailable 
         TX Power = Unavailable 
         RX Power = Unavailable 




