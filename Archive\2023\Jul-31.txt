

=======================
ACTIVATION
=======================
MVT2905803
MVT2905882


1004540778 - bve
1004729660 - bve
OSite_593559_1 - MVT2905771 - MVT2905778 (rerun)
OSite_477625_1 - MVT2905877 (rerun)


================

Dean - bve 
2359
35895295
183166


Delan - bve
3012
35844168
182636


Enrique - bve
2270
35785816
182714


Wilson - bve
5095
35711082
transport change


Tremayne - activate
1819
1004339096
accepting


closure failed
dlawso001
1004213496
not accepting










================================
SURVEYS - INFORMATION - NOT DONE
================================












===== TTU =====

Customer: Peninsula Trucking - 1004540778
Service: BVE
CPE: **************
UNI: 2
VLAN: 3991

-loaded config on port 2
-enable ae61.3991
-created ip-int and deleted after pinging

Pinging *********** with 32 bytes of data:                                                                              
Reply from ***********: bytes=32 time=83ms TTL=44                                                                       
Reply from ***********: bytes=32 time=77ms TTL=44                                                                       
Reply from ***********: bytes=32 time=87ms TTL=44                                                                       
Reply from ***********: bytes=32 time=81ms TTL=44  


interface create ip-interface av-ref ip ***********/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3991


set groups BVE-TEST-L3IRB interfaces irb unit 3903 family inet address *************/30
set groups BVE-TEST-L3IRB bridge-domains vlan3903 routing-interface irb.3903
set apply-groups BVE-TEST-L3IRB




















===== TTU =====

Customer: Baptist Health South Florida  - **********
Service: EPL
CPE: **************
EVC: 38.VLXP.056507..CBCL..
MVT: MVT2904056

-removed cpe from monitoring service
 ce-wpbiflle00w.cpe.fl.pompano.comcast.net 	************** 	Decommission 	MVT2904056 	7/26/2023 11:27 PM

-multiple services on MIAMFLFR04W





-unable to remove from MVT - another active service on port 2



















