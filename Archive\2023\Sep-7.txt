

================================
ACTIVATION
================================

MVT2915477
MVT2915507
MVT2917458
MVT2920546
MVT2921014
MVT2921149



1003964102 - disco


Brian - customer - complete in orion
9412841642
OSite_711348_1
accepting


================================



Kennie - activate 
3463
1004524228
ch20
enable TenGigE0/0/0/10
enable TenGigE0/0/0/10.1000
enable TenGigE0/0/0/10.3733
enable Bundle-Ether1.3733
enable Bundle-Ether114.3733
accepting


Andy - activate
3320
1004467090
accepting


Gio - activate
2293
1004490764


Sadoc - activate
2730
1004743892
not accepting


Mike - activate
2209
OSite_674372_1


Scott - activate
4028617529
OSite_714149_1


Donald - activate
1204
1003290606
accepting


Dan - activate
4067
1004537284
activate
accepting


Lyndon - activate
3505
1004688732
not accepting


Alfredo - bve
4793204823
36183150



















================================
SURVEYS - INFORMATION - NOT DONE
================================


Hasen Brench - customer
3057757609
1004354662
accepting



<EMAIL>















================================
			UNDERLAY
================================

++++ TTU ++++

Customer: Rutherford Farmers Co Op - OSite_693876_1
Service: BVE
CPE: **************
UNI: 1
VLAN: 3838

-loaded config on port 1
-created ip-int and deleted after pinging
-enable GigabitEthernet0/0/0/29.3838
-enable Bundle-Ether1.3838
-match negotiation

Pinging *********** with 32 bytes of data:                                            
Reply from ***********: bytes=32 time=69ms TTL=51                                     
Reply from ***********: bytes=32 time=75ms TTL=51                                     
Reply from ***********: bytes=32 time=68ms TTL=51                                     
Reply from ***********: bytes=32 time=69ms TTL=51 

interface create ip-interface av-ref ip ***********/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3838


set groups BVE-TEST-L3IRB interfaces irb unit 3891 family inet address ***********/30
set groups BVE-TEST-L3IRB bridge-domains vlan3891 routing-interface irb.3891
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB






================================
			DISCONNECTS
================================

++++ TTU ++++

Customer: Indiana Office of Technology - **********
Service: ENS
CPE: eg-iplvinea36w.733henry.in.indiana.comcast.net
UNI: 23.KGGS.016568..CBCL..
UNI: xe-0/1/2
VLAN: 1507

Link is up with live traffic - advised the PM - PM will reach out to the customer - disco on hold


Interface       Admin Link Description
xe-0/1/2        up    up   PHY|10G|BIZ-UNI|dtype:BIZ-CUST|uni-id:23.KGGS.016568..CBCL..|cust-acct:*********|comments:Indiana


Interface: xe-0/1/2, Enabled, Link is Up
Encapsulation: VPLS, Speed: 10000mbps
Traffic statistics:                                              Current delta
  Input bytes:           *************** (********* bps)          [**********]
  Output bytes:           ************** (********* bps)           [*********]
  Input packets:            ************ (31771 pps)                 [2150848]
  Output packets:            96415865586 (14513 pps)                 [1046955]



================================
			 MVT
================================

++++ TTU ++++

Customer: Atos IT Solutions & Services - 1004734648
Service: EPL
CPE: *************
EVC: 30.VLXP.942560..CBCL..
MVT: **********

-removed cpe from monitoring service





-multiple services on MIAMFLFR04W





-unable to remove from MVT - another active service on port 2



















