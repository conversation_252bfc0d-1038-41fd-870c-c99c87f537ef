

================================
ACTIVATION
================================
MVT2908583





Chris - customer
7722212904
36.krgs.039431
1004436910
accepting


================================

Jean - activate | fix fqdn
4602
OSite_710786_1
ch24
enable Bundle-Ether1.3976
enable TenGigE0/0/0/21
enable Bundle-Ether140.3976
accepting


thony - activate
1522
**********
enable GigabitEthernet0/0/0/3.3732
enable Bundle-Ether1.3732


Gio - activate
2293
**********
accepting


travis  - activate
4051
OSite_708173_1
accepting


Tim - activate
1006
**********
enable Bundle-Ether1.1514
not accepting




























================================
SURVEYS - INFORMATION - NOT DONE
================================


Jassie - customer
**********
****************
********
182788



















===== TTU =====

Customer: First Century Bank - *********
Service: SIP
CPE: *************
UNI: ge-0/0/1
VLAN: 3891

-loaded config on port 2
-created ip-int and deleted after pinging

Reply from ***********: bytes=32 time=72ms TTL=50                                               
Reply from ***********: bytes=32 time=75ms TTL=50                                               
Reply from ***********: bytes=32 time=71ms TTL=50                                               
Reply from ***********: bytes=32 time=71ms TTL=50


interface create ip-interface av-ref ip ************/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3900


set groups BVE-TEST-L3IRB interfaces irb unit 3891 family inet address ***********/30
set groups BVE-TEST-L3IRB bridge-domains vlan3891 routing-interface irb.3891
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB



















===== TTU =====

Customer: Baptist Health South Florida  - **********
Service: EPL
CPE: **************
EVC: 38.VLXP.056507..CBCL..
MVT: MVT2904056

-removed cpe from monitoring service
 ce-wpbiflle00w.cpe.fl.pompano.comcast.net 	************** 	Decommission 	MVT2904056 	7/26/2023 11:27 PM

-multiple services on MIAMFLFR04W





-unable to remove from MVT - another active service on port 2



















