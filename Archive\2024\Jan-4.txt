
cadapass auth -a netengmguzma002 -s XgBJZ14mw2IuCYBD6O6Z7KLED9LJks00

jump cada --password-only --username=mguzma002 --env=prod null
eval $(ssh-agent)
ssh-add

Y*LCAh(5dePEsEY5*_jpBaKmf7hKAf1a

29O739L6
================================
ACTIVATION
================================

MVT2915477
MVT2922695
MVT2923263
MVT2927179


MVT2931986
MVT2934080
MVT2937316
MVT2938968
MVT2948560 - fix ce-spfdilti00w.cpe.il.chicago.comcast.net - 96.204.226.6
MVT2950902 - fix




End Of Day Orders 
=================




MVT
===



Disco
=====





BandWidth Upgrade
=================



Underlay
========



Service Accepting
=================




Jira Tickets
=================



================================
             ORDERS
================================

Anthony - 2385 - 1004944996 - EPL

Brent - 6409 - 1004878550

Travis - 4067 - OSite_728397_1

Robert - OTT - 5109 - 36186877 / 192222 - BVE

Bent - 3207 - OSite_706544_1 - EDI

Dan - 4067 - OSite_704212_1  - EDI

Donald - 1204 - OSite_695246_1 - EDI

Jim - 2666 - OSite_724292_1 - EDI

EOD - 1004958202








================================
ORDERS TO CHECK TOMORROW
================================
OSite_550600_1



EPL-Watch_F-ACCT-900018255-VRF-5413770
EPL-ACCT900018255-VRF5413770


================================
SURVEYS - INFORMATION - NOT DONE
================================





================================
STUDY - STUDY - STUDY - STUDY
================================


















================================
	    UNDERLAY
================================

++++ TTU ++++

Customer: DREAM MARKET - OSite_680831_1 
Service: BVE
CPE: *************
UNI: port 2
VLAN: 3999

-created virtual interface and deleted after pinging
-p2p pinging on virtual interface
-enable GigabitEthernet0/0/0/5.3999

    

interface create ip-interface av-ref ip ************/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3999



set groups BVE-TEST-L3IRB interfaces irb unit 3990 family inet address ************/30
set groups BVE-TEST-L3IRB bridge-domains vlan3990 routing-interface irb.3990
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB







================================
	     OTHERS
================================

++++ TTU ++++

Customer: The Colony Hotel Palm Beach - **********
Service: EDI
CPE: ************** - PLBHFLIJ03W
UNI: Port 1
VLAN: 3808

Notes
=======

According to desing the CE should connect to sag12 on port GE-0/0/0/30
but there is no sfp slotted on port GE-0/0/0/30 - preconfig is loaded
order is rejected for no CE online and no SFP slotted at the HE




RP/0/RP0/CPU0:sag12.northpoint.fl.pompano#sh controll opt 0/0/0/30
Wed Sep 13 20:55:52.342 UTC

 Controller State: Down 

 Transport Admin State: In Service 

 Laser State: Off 

         Optics not present
         Optics Type: Unavailable
         DWDM Carrier Info: Unavailable, MSA ITU Channel= Unavailable, Frequency= Unavailable , Wavelength= Unavailable 
         TX Power = Unavailable 
         RX Power = Unavailable 




