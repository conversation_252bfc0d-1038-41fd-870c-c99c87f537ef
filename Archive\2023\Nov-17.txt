
cadapass auth -a netengmguzma002 -s XgBJZ14mw2IuCYBD6O6Z7KLED9LJks00
eval $(ssh-agent)
ssh-add

jump cada --password-only --username=MGuzma002 --env=prod null

)60aNBDj#TBOonRksYu9Ap@8vDJV!8fr

d fsd fsdfs
================================
ACTIVATION
================================

MVT2915477
MVT2915507
MVT2921014
MVT2921149
MVT2922450
MVT2922695
MVT2922714
MVT2922721
MVT2923263
MVT2926800
MVT2927179
MVT2931986
MVT2934080
MVT2937316
MVT2938968
MVT2948560 - fix ce-spfdilti00w.cpe.il.chicago.comcast.net - 96.204.226.6





my ticket SR019361257


End Of Day Orders
=================




MVT
===
1004891926
1004886432
1004827416
1004902088
-
1004850078
1004849770
1004866778
1004819066
-
1004699390
1004638824
1004655688
1004385018
-
1004328392






Disco
=====





BandWidth Upgrade
=================





Underlay
========




Service Accepting
=================




Jira Tickets
=================




================================
             ORDERS
================================


Lorenzo
2725
1004413206
enable Bundle-Ether1.3809
enable GigabitEthernet0/0/0/30.3809
a


Rosendo - bve
1755
34942438
183361


Gio
2293
OSite_662689_1
n


Mike
1459
OSite_726155_1
a


Thony
3493
1004328392
a


Kevin - bve
4098
36518021
187310





EPL-930903044-3011208







================================
SURVEYS - INFORMATION - NOT DONE
================================

Cupaqu
900007065
31.krgs.042142



1/1/c8/1









================================
	    UNDERLAY
================================

++++ TTU ++++

Customer: DREAM MARKET - OSite_680831_1 
Service: BVE
CPE: *************
UNI: port 2
VLAN: 3999

-created virtual interface and deleted after pinging
-p2p pinging on virtual interface
-enable GigabitEthernet0/0/0/5.3999

    

interface create ip-interface av-ref ip ************/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3999



set groups BVE-TEST-L3IRB interfaces irb unit 3990 family inet address ************/30
set groups BVE-TEST-L3IRB bridge-domains vlan3990 routing-interface irb.3990
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB







================================
	     OTHERS
================================

++++ TTU ++++

Customer: The Colony Hotel Palm Beach - **********
Service: EDI
CPE: ************** - PLBHFLIJ03W
UNI: Port 1
VLAN: 3808

Notes
=======

According to desing the CE should connect to sag12 on port GE-0/0/0/30
but there is no sfp slotted on port GE-0/0/0/30 - preconfig is loaded
order is rejected for no CE online and no SFP slotted at the HE




RP/0/RP0/CPU0:sag12.northpoint.fl.pompano#sh controll opt 0/0/0/30
Wed Sep 13 20:55:52.342 UTC

 Controller State: Down 

 Transport Admin State: In Service 

 Laser State: Off 

         Optics not present
         Optics Type: Unavailable
         DWDM Carrier Info: Unavailable, MSA ITU Channel= Unavailable, Frequency= Unavailable , Wavelength= Unavailable 
         TX Power = Unavailable 
         RX Power = Unavailable 




