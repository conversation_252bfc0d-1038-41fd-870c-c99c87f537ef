
cadapass auth -a netengmguzma002 -s XgBJZ14mw2IuCYBD6O6Z7KLED9LJks00

jump cada --password-only --username=mguzma002 --env=prod null
eval $(ssh-agent)
ssh-add


================================
ACTIVATION
================================


End Of Day Orders 
=================




MVT
===



Disco
=====




BandWidth Upgrade
=================



Underlay
========



Service Accepting
=================




Jira Tickets
=================




================================
             ORDERS
================================
 MVT2972180 - 
 

Juan - 2007 - OSite_680129_1 - waiting

==========

John - 5066 - 1004933098 - EDI

Sadoc - 2730 - 1004547392 - ENS - add notes and complete

Brian - 3217 - 1004748358 - EDI

Dan - 4067 - 1004755990 - ENS  

Dan - 4067 - 1004755988 - ENS

Enrique - 2270 - 37331541/192650 - BVE

Mike - 2427 - 1004747956 - EDI

Adam - 2670 - 1004675736 - ENS

Ray - 3286 - 1004739570 - EDI

Gio - 2293 - 1004535516 - EDI

Cisco - 2532 - OSite_709596_1 - EDI

Don - 5070 - OSite_710869_1 - EDI 

Chris - 4355 - 37418180/192908 - BVE

EOD - 1005004552



traffic-services queuing egress-port-queue-group set port 1 scheduler-algorithm weighted-round-robin shaper-rate 220000 burst-size 2500

cir 220000 pir 220000 cbs 2500 ebs 0 dscp-remark-policy fixed fixed-dscp 8 untagged






================================
ORDERS TO CHECK TOMORROW
================================

Phil - 1344 - OSite_670319_1 - no light both way




benchmark set port 2
benchmark set role reflector
benchmark set mode out-of-service
benchmark enable
benchmark reflector enable
int enable ip-interface EDIA-3900-ref






================================
SURVEYS - INFORMATION - NOT DONE
================================
Jim - 4794 - 1004898454 - EPL - waiting




================================
STUDY - STUDY - STUDY - STUDY
================================















================================
	    UNDERLAY
================================

++++ TTU ++++

Customer: DREAM MARKET - OSite_680831_1 
Service: BVE
CPE: *************
UNI: port 2
VLAN: 3999

-created virtual interface and deleted after pinging
-p2p pinging on virtual interface
-enable GigabitEthernet0/0/0/5.3999

    

interface create ip-interface av-ref ip ************/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3999



set groups BVE-TEST-L3IRB interfaces irb unit 3990 family inet address ************/30
set groups BVE-TEST-L3IRB bridge-domains vlan3990 routing-interface irb.3990
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB







================================
	     OTHERS
================================

++++ TTU ++++

Customer: The Colony Hotel Palm Beach - **********
Service: EDI
CPE: ************** - PLBHFLIJ03W
UNI: Port 1
VLAN: 3808

Notes
=======

According to desing the CE should connect to sag12 on port GE-0/0/0/30
but there is no sfp slotted on port GE-0/0/0/30 - preconfig is loaded
order is rejected for no CE online and no SFP slotted at the HE




RP/0/RP0/CPU0:sag12.northpoint.fl.pompano#sh controll opt 0/0/0/30
Wed Sep 13 20:55:52.342 UTC

 Controller State: Down 

 Transport Admin State: In Service 

 Laser State: Off 

         Optics not present
         Optics Type: Unavailable
         DWDM Carrier Info: Unavailable, MSA ITU Channel= Unavailable, Frequency= Unavailable , Wavelength= Unavailable 
         TX Power = Unavailable 
         RX Power = Unavailable 




