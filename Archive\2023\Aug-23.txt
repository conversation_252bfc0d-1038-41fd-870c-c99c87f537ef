

================================
ACTIVATION
================================
MVT2913235
MVT2914420
MVT2914557
MVT2915477
MVT2915507



================================


Scott - activate - complete in orion
1244
OSite_706386_1
ce-cpcrfl7000w.cpe.fl.naples.comcast.net


Dan - activate
4067
**********
enable Bundle-Ether1.3756
accepting


tremayne - activate
1819
**********
accepting


Castery - bve OTT
**********
********


Larry - activate
4391
**********
not accepting


Gio - activate
2293
OSite_690612_1
accepting















================================
SURVEYS - INFORMATION - NOT DONE
================================


Angel - survey
1325
**********
verify port up


Kenin - 
**********
12.krgs.063855
















===== TTU =====

Customer: First Century Bank - *********
Service: SIP
CPE: *************
UNI: ge-0/0/1
VLAN: 3891

-loaded config on port 2
-created ip-int and deleted after pinging

Reply from ***********: bytes=32 time=72ms TTL=50                                               
Reply from ***********: bytes=32 time=75ms TTL=50                                               
Reply from ***********: bytes=32 time=71ms TTL=50                                               
Reply from ***********: bytes=32 time=71ms TTL=50


interface create ip-interface av-ref ip ************/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3900


set groups BVE-TEST-L3IRB interfaces irb unit 3891 family inet address ***********/30
set groups BVE-TEST-L3IRB bridge-domains vlan3891 routing-interface irb.3891
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB



















===== TTU =====

Customer: Baptist Health South Florida  - **********
Service: EPL
CPE: **************
EVC: 38.VLXP.056507..CBCL..
MVT: MVT2904056

-removed cpe from monitoring service
 ce-wpbiflle00w.cpe.fl.pompano.comcast.net 	************** 	Decommission 	MVT2904056 	7/26/2023 11:27 PM

-multiple services on MIAMFLFR04W





-unable to remove from MVT - another active service on port 2



















