def calculate_trading_ratios(entry, stop_loss, take_profit):
    """
    Calculate various trading ratios for risk management.
    
    Args:
        entry (float): Entry price
        stop_loss (float): Stop loss price
        take_profit (float): Take profit price
    
    Returns:
        dict: Dictionary containing various ratio calculations
    """
    # Calculate risk and reward
    risk = abs(entry - stop_loss)
    reward = abs(take_profit - entry)
    
    # Calculate ratios
    risk_reward_ratio = reward / risk if risk != 0 else float('inf')
    sl_to_entry_ratio = stop_loss / entry if entry != 0 else 0
    tp_to_entry_ratio = take_profit / entry if entry != 0 else 0
    
    # Calculate percentages
    risk_percentage = (risk / entry) * 100 if entry != 0 else 0
    reward_percentage = (reward / entry) * 100 if entry != 0 else 0
    
    return {
        'entry': entry,
        'stop_loss': stop_loss,
        'take_profit': take_profit,
        'risk_points': risk,
        'reward_points': reward,
        'risk_reward_ratio': f"1:{risk_reward_ratio:.2f}",
        'risk_reward_decimal': risk_reward_ratio,
        'sl_to_entry_ratio': sl_to_entry_ratio,
        'tp_to_entry_ratio': tp_to_entry_ratio,
        'risk_percentage': f"{risk_percentage:.2f}%",
        'reward_percentage': f"{reward_percentage:.2f}%"
    }

def print_trading_analysis(entry, stop_loss, take_profit):
    """
    Print a formatted analysis of trading ratios.
    """
    ratios = calculate_trading_ratios(entry, stop_loss, take_profit)
    
    print("=" * 50)
    print("TRADING RATIO ANALYSIS")
    print("=" * 50)
    print(f"Entry Price:     ${ratios['entry']}")
    print(f"Stop Loss:       ${ratios['stop_loss']}")
    print(f"Take Profit:     ${ratios['take_profit']}")
    print("-" * 50)
    print(f"Risk (points):   {ratios['risk_points']}")
    print(f"Reward (points): {ratios['reward_points']}")
    print(f"Risk %:          {ratios['risk_percentage']}")
    print(f"Reward %:        {ratios['reward_percentage']}")
    print("-" * 50)
    print(f"Risk:Reward Ratio: {ratios['risk_reward_ratio']}")
    print(f"SL/Entry Ratio:    {ratios['sl_to_entry_ratio']:.3f}")
    print(f"TP/Entry Ratio:    {ratios['tp_to_entry_ratio']:.3f}")
    print("=" * 50)

# Example usage with your values
if __name__ == "__main__":
    # Your example values
    entry = 10
    sl = 5
    tp = 40
    
    print("Example calculation:")
    print_trading_analysis(entry, sl, tp)
    
    print("\nDetailed ratios:")
    ratios = calculate_trading_ratios(entry, sl, tp)
    for key, value in ratios.items():
        print(f"{key}: {value}")
