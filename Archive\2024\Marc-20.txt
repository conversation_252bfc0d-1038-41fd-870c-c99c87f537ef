

cadapass auth -a netengmguzma002 -s XgBJZ14mw2IuCYBD6O6Z7KLED9LJks00https://ev01.b.comcast.net/menu.html

jump cada --password-only --username=mguzma002 --env=prod null
eval $(ssh-agent)
ssh-add


<PERSON> - 4109
<PERSON> - 2910
Dan <PERSON> - 4067
<PERSON> - 3640
<PERSON> - 3751
Aaron - 5075
<PERSON> - 4733
Travis <PERSON> - 4051
Christ Chong - 2041
Sadoc - 2730
Alex - 1499
<PERSON> 5095
<PERSON> 2425
Mike <PERSON> 1459
<PERSON> 4138
<PERSON> 3104
Luis 2333 hialeah
<PERSON> 1132 
<PERSON> 5732
Andy  <PERSON> 3320

================================
             ORDERS
================================

Jaime - 1162 1004210092 customer accepting - complete order when task is ready
EOD 1004761030 waiting on PM old CA has not been removed - PM was waiting on activecore to be canceled
EOD 1004941810 verify if it was built under the correct EVC
EOD 1004996542 add demarc from tech
work day - side jobs gigs

-

Ivan 2610 - 1004945662 ENS

EOD 1004881756 EDI N

Vaun 4048589709 37036698 PRI DOC

EOD 1004737728 

EOD 1004815396 EDI









+------------------------- FLOW MAC-LEARN TABLE -------------------------+
| VLAN   | Address (SA)      | Port            | Type  | Virtual Switch  |
+--------+-------------------+-----------------+-------+-----------------+
| 3797   | C0:2C:17:31:3B:0A | 8               | Dyna  | Frankli_3797    |
| 3797   | C0:2C:17:31:3C:4A | 1               | Dyna  | Frankli_3797    |
| 3797   | E4:2B:79:C6:DD:37 | 8               | Dyna  | Frankli_3797    |
+--------+-------------------+-----------------+-------+-----------------+






================================
SURVEYS - INFORMATION - NOT DONE
================================
Tim 37549970
Erin 3178 OSite_761424_1
Jen 37549970 BVE OTT




Service Accepting
=================



================================
OTHER ORDERS COMPLETED
================================





================================
	    UNDERLAY
================================

++++ TTU ++++

Customer: DREAM MARKET - OSite_680831_1 
Service: BVE
CPE: *************
UNI: port 2
VLAN: 3999

-created virtual interface and deleted after pinging
-p2p pinging on virtual interface
-enable GigabitEthernet0/0/0/5.3999

    

interface create ip-interface av-ref ip ***********/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3760



set groups BVE-TEST-L3IRB interfaces irb unit 3990 family inet address ************/30
set groups BVE-TEST-L3IRB bridge-domains vlan3990 routing-interface irb.3990
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB







================================
	     OTHERS
================================

++++ TTU ++++

Customer: The Colony Hotel Palm Beach - **********
Service: EDI
CPE: ************** - PLBHFLIJ03W
UNI: Port 1
VLAN: 3808

Notes
=======

According to desing the CE should connect to sag12 on port GE-0/0/0/30
but there is no sfp slotted on port GE-0/0/0/30 - preconfig is loaded
order is rejected for no CE online and no SFP slotted at the HE




RP/0/RP0/CPU0:sag12.northpoint.fl.pompano#sh controll opt 0/0/0/30
Wed Sep 13 20:55:52.342 UTC

 Controller State: Down 

 Transport Admin State: In Service 

 Laser State: Off 

         Optics not present
         Optics Type: Unavailable
         DWDM Carrier Info: Unavailable, MSA ITU Channel= Unavailable, Frequency= Unavailable , Wavelength= Unavailable 
         TX Power = Unavailable 
         RX Power = Unavailable 




