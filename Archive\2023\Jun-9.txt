

=======================
ACTIVATION
=======================

wiston delgado - customer accepting
1004485770


Eugene - activate - waiting on tech
4602
OSite_705224_1
enable GigabitEthernet0/0/0/15.3995
nothing slotted
accepting


Nelson - customer
8473063777 ext 8405
1004316468
accepting


Mike - activate
1459
1004536712
accepting


Roger - activate
1132
OSite_693395_1
enable Bundle-Ether1.3765
not accepting


terrence - activate
3678
1004545142
not accepting




















================================
SURVEYS - INFORMATION - NOT DONE
================================


Brian - survey
3217
2456422
tune ch52


David - customer
9542578991
OSite_676853_1
wanted to verify ip













===== TTU =====

Customer: 
Service: 
CPE: 
UNI: 
VLAN: 
EDGE/IAD ip: 

-loaded config on port 1
-created ip-int with edge ip under vlan 3849
-deleted ip-int after testing





interface create ip-interface EDIA-3849-ref ip ***********/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3849




===== TTU =====

Customer: 
Service: 
CPE: 
EVC: 

-removed cpe from monitoring service

