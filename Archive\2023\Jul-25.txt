

=======================
ACTIVATION
=======================




Warner - customer
3127145050
1001370224
accepting


=======================



gio - activate
2293
OSite_718800_1
not accepting


Rod - activate
4100
OSite_702955_1
enable Bundle-Ether1.3910
enable GigabitEthernet0/0/0/32.3910
accepting


James - activate
2503
OSite_704036_1
enable GigabitEthernet0/0/0/8.3765
enable Bundle-Ether1.3765
accepting


closure failed - activate
Lorenzo - 2725
1004543996
not accepting


Brian - activate
3217
OSite_710406_1
not accepting























================================
SURVEYS - INFORMATION - NOT DONE
================================

Andrew - customer
6083202000
1004589064 - 














===== TTU =====

Customer: STEWART FAMILY PRODUCE - OSite_705503_1
Service: BVE
CPE: *************
UNI: ge-0/0/0
VLAN: 3903

-loaded config on port 1
-created ip-int and deleted after pinging
                                
Pinging ************* with 32 bytes of data:                                             
Reply from *************: bytes=32 time=68ms TTL=45                                      
Reply from *************: bytes=32 time=70ms TTL=45                                      
Reply from *************: bytes=32 time=69ms TTL=45                                      
Reply from *************: bytes=32 time=69ms TTL=45



interface create ip-interface av-ref ip ************/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3654


set groups BVE-TEST-L3IRB interfaces irb unit 3903 family inet address *************/30
set groups BVE-TEST-L3IRB bridge-domains vlan3903 routing-interface irb.3903
set apply-groups BVE-TEST-L3IRB




















===== TTU =====

Customer: ENA-ME-IL-*********  - **********
Service: ENS
CPE: **************
EVC: 22.VLXM.009401..CBCL..

-removed cpe from monitoring service

ce-okfoilgr00w.cpe.il.chicago.comcast.net	**************	Decommission	MVT2898878	7/13/2023 10:42 PM







-unable to remove from MVT - another active service on port 2



















