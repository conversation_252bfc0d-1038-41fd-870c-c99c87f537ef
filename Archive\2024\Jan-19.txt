
cadapass auth -a netengmguzma002 -s XgBJZ14mw2IuCYBD6O6Z7KLED9LJks00

jump cada --password-only --username=mguzma002 --env=prod null
eval $(ssh-agent)
ssh-add


================================
             ORDERS
================================

Sadoc - 2730 - 1004547392 - ENS 

Mike - 5679 - 1004882126 - EDI

Juan - 2007 - OSite_680129_1 - EDI

Josh - 5076 - 1004205290 - EDI

Gio - 2293 - 1004625152 - EDI

Charles - 3425 - 37388777/192923 - BVE

Lorenzo - 2725 - 1004641404 - EDI 

Juan - 2007 - OSite_712419_1 - EDI

Gio - 2293 - 1004625104 - EPL

Diego - 2289 - 37283597/193197 - BVE

Larry - 2217 - 37265915/192234 - BVE

Chris - 2041 - 36742008/193238 - BVE

Balford - 3354 - 1004873374 - <PERSON><PERSON>

EOD - OSite_695246_1















End Of Day Orders 
=================




MVT
===
OSite_723278_1 - fix MVT2973686



Disco
=====




BandWidth Upgrade
=================



Underlay
========



Service Accepting
=================
Bob - 4072037327 - 1004796778





Jira Tickets
=================



================================
ORDERS TO CHECK TOMORROW
================================




================================
SURVEYS - INFORMATION - NOT DONE
================================




================================
STUDY - STUDY - STUDY - STUDY
================================















================================
	    UNDERLAY
================================

++++ TTU ++++

Customer: DREAM MARKET - OSite_680831_1 
Service: BVE
CPE: *************
UNI: port 2
VLAN: 3999

-created virtual interface and deleted after pinging
-p2p pinging on virtual interface
-enable GigabitEthernet0/0/0/5.3999

    

interface create ip-interface av-ref ip ************/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3999



set groups BVE-TEST-L3IRB interfaces irb unit 3990 family inet address ************/30
set groups BVE-TEST-L3IRB bridge-domains vlan3990 routing-interface irb.3990
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB







================================
	     OTHERS
================================

++++ TTU ++++

Customer: The Colony Hotel Palm Beach - **********
Service: EDI
CPE: ************** - PLBHFLIJ03W
UNI: Port 1
VLAN: 3808

Notes
=======

According to desing the CE should connect to sag12 on port GE-0/0/0/30
but there is no sfp slotted on port GE-0/0/0/30 - preconfig is loaded
order is rejected for no CE online and no SFP slotted at the HE




RP/0/RP0/CPU0:sag12.northpoint.fl.pompano#sh controll opt 0/0/0/30
Wed Sep 13 20:55:52.342 UTC

 Controller State: Down 

 Transport Admin State: In Service 

 Laser State: Off 

         Optics not present
         Optics Type: Unavailable
         DWDM Carrier Info: Unavailable, MSA ITU Channel= Unavailable, Frequency= Unavailable , Wavelength= Unavailable 
         TX Power = Unavailable 
         RX Power = Unavailable 




