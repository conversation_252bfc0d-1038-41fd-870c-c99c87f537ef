

=======================
ACTIVATION
=======================

mvt remove

1004612012
1004617556
1004590332
1004524814
-
1004528340
1004501046




Will - active
2254
OSite_702589_1
fix - ce-agsvgans00w.cpe.ga.augusta.comcast.net - *************
enable GigabitEthernet0/0/0/27.3853
enable Bundle-Ether1.3853
enabel ae113.3853


Gio - activate
2293
OSite_682905_1
accepting


Dave - activate
2033
OSite_694465_1
not accepting


Jeff - eohfc - complete in orion
3104
OSite_706259_1


eugene - MTG
bsne-23464


Juan - activate
2007
1004616426
not accepting











================================
SURVEYS - INFORMATION - NOT DONE
================================

Rachill - customer
5145505479
1004619552
gw: 2001:559:7000::1ed
ip: 2001:559:7000::1ee

ipv6-prefix-list "IPV6-EDIA-BGP-PEERS-PFX"
"IPV6-DEFAULT-PFX"





Eugene - MTG
4602
bsne-23464
Bundle-Ether65
BE65               down        down        
Te0/0/0/14         down        down        
Te0/0/0/15         down        down 













===== TTU =====

Customer: Tinley Park School District-METRO E
Service: BVE
CPE: **************
UNI: 1
VLAN: 3629

-loaded config on port 3
-created ip-int with edge ip under vlan 3669
-deleted ip-int after testing
                                                      
ce-orpkilpd03w.cpe.il.chicago> flow mac-addr sh port 1
Getting MAC entries, please be patient...

+------------------------- FLOW MAC-LEARN TABLE -------------------------+
| VLAN   | Address (SA)      | Port            | Type  | Virtual Switch  |
+--------+-------------------+-----------------+-------+-----------------+
| 3629   | 54:39:68:20:FD:BB | 1               | Dyna  | ---             |
+--------+-------------------+-----------------+-------+-----------------+


interface create ip-interface EDIA-3669-ref ip *************/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3669




===== TTU =====

Customer: 
Service: 
CPE: 
EVC: 

-removed cpe from monitoring service







































