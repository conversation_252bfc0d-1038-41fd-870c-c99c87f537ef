

================================
ACTIVATION
================================

MVT2915477
MVT2915507
MVT2917458

1004285308 - bve complete in century
********** - bve
1004285312 - bve
1004758936 - bve




================================


Gio - activate - complete order
2293
OSite_705788_1


Jamie - activate
6476
1004707528
ch24
enable TenGigE0/3/0/16.1000
enable TenGigE0/3/0/16.3955
accepting


Paul - activate 
2425
1004332810
accepting


Paul - activate
2425
OSite_713933_1
enable GigabitEthernet0/0/0/7.3987


Gio - activate
2293
1004512614
not accepting


Asic - PRI DOC
30994226


ttu - activate
1004673866


ttu - activate
1004702180




================================
SURVEYS - INFORMATION - NOT DONE
================================


Jeff - survey
3104
1004518142
ch21 - already tuned


Ben - activate
3207
1004167006
ch40













===== TTU =====

Customer: Village of Maywood - **********
Service: BVE
CPE: **************
UNI: 1
VLAN: 3977

-loaded config on port 1
-created ip-int and deleted after pinging

Pinging ************ with 32 bytes of data:                                                         Reply from ************: bytes=32 time=57ms TTL=54                                                  Reply from ************: bytes=32 time=60ms TTL=54                                                  Reply from ************: bytes=32 time=59ms TTL=54                                                  Reply from ************: bytes=32 time=61ms TTL=54  


interface create ip-interface av-ref ip ***********/30 service-mac benchmark mtu 1522 ip-forwarding on vlan 3977


set groups BVE-TEST-L3IRB interfaces irb unit 3891 family inet address ***********/30
set groups BVE-TEST-L3IRB bridge-domains vlan3891 routing-interface irb.3891
set apply-groups BVE-TEST-L3IRB
commit



delete apply-groups BVE-TEST-L3IRB
delete groups BVE-TEST-L3IRB







===== TTU =====

Customer: Village of Maywood - **********
Service: BVE
CPE: **************
UNI: 1
VLAN: 3939

Already traffic on port 1 - edge already connected and reachable








===== TTU =====

Customer: Marquee Insurance Group  - **********
Service: EDI
CPE: **************
EVC: 30.VLXP.075654..CBCL..
MVT: MVT2917327

-removed cpe from monitoring service
ce-rswlgaiw03w.cpe.ga.atlanta.comcast.net 	************** 	Decommission 	MVT2917327 	8/28/2023 4:40 PM



-multiple services on MIAMFLFR04W





-unable to remove from MVT - another active service on port 2



















